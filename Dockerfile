# Use the latest slim version of Kali Linux
FROM kali_2025.2_b0:latest

# Copy contents of the project to /
COPY . /

WORKDIR /heka

# install python after packages to ensure version overriding
RUN bash /docker/ins/install_python.sh

# configure ssh
RUN bash /docker/ins/configure_ssh.sh

# after install
RUN bash /docker/ins/after_install.sh

# Esporre porte se necessario
EXPOSE 22

# Comando di default
CMD ["/bin/bash"]