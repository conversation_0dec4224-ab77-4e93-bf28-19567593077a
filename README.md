# Heka

**Heka** è uno strumento AI che utilizza agenti autonomi basati per automatizzare task di bug bounty e penetration testing su web app. Sviluppato da **b0**, Heka è progettato per funzionare sia su GPU che su CPU, offrendo un ambiente flessibile e potente per la ricerca di vulnerabilità.  
Inoltre, Heka può essere eseguito all'interno di un container Docker basato su Kali Linux, offrendo un ambiente isolato e pronto all'uso per l'esecuzione dei comandi.

## Caratteristiche

- Agenti AI autonomi per hacking e bug bounty
- Facile da installare e configurare
- Esecuzione dei comandi in un container Docker Kali Linux

## Installazione

### Prerequisiti

- [Conda](https://docs.conda.io/en/latest/)
- Python 3.12
- [Docker](https://www.docker.com/) (per esecuzione in container)

### Creazione ambiente

```bash
conda create -n heka python=3.12
conda activate heka
```

```bash
pip install playwright requests beautifulsoup4 python-dotenv
playwright install
```

## Esecuzione

1. Costruisci l'immagine Docker:

```bash
docker build -t heka --build-arg CACHE_DATE=$(date +%Y-%m-%d:%H:%M:%S)  .
```

### Esecuzione su Conda

1. Crea uno script `run.sh`:

```bash
nano run.sh
```

2. Inserisci il seguente contenuto:

```bash
#!/bin/bash

# Esporta il token Hugging Face (gestiscilo con cautela)
export HF_TOKEN=*************************************

# Imposta la directory di lavoro
cd /media/b0/dev/heka/

# Attiva l'ambiente Conda e avvia l'applicazione Python
source /media/b0/dev/miniconda3/etc/profile.d/conda.sh
conda activate heka
python app.py
```

3. Rendi eseguibile lo script:

```bash
chmod +x run.sh
```

4. Avvia Heka:

```bash
./run.sh
```


